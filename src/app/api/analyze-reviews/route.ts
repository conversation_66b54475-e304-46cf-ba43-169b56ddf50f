import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const { appReviews, messages } = await request.json(); // Destructure messages from request body

    if (!appReviews) {
      return NextResponse.json({ message: 'App reviews are required.' }, { status: 400 });
    }

    const prompt = `
      Analyse the provided collection of app reviews and complete the following tasks in this order:

      1. Summarize Key Findings
      * Provide an overall summary of the good aspects praised by users.
      * Provide an overall summary of the bad aspects or complaints raised by users.

      2. Generate Improvement Recommendations
      * Based on the insights gathered from the positive and negative reviews, provide 10 actionable recommendations to improve the app.
      * Each recommendation should address exactly one specific feature or aspect of the app.
      * Recommendations should be practical and feasible for implementation.

      3. Extract Review Metadata
      For each review, include a citation of the date and time it was posted, if available.

      4. Categorize Reviews by Sentiment
      * Label each review as Positive or Negative based on the overall sentiment.
      * For Positive reviews, assign a Positivity Score on a scale of 1 to 10 (where 10 means the review highlights the strongest positive aspect).

      5. Present the Results
      Create two tables:
      Table 1: Negative Reviews
      Username	Date & Time	Complaint/Issue	Citation (Quote or Summary)	Severity (1-10)
      [Username]	[YYYY-MM-DD HH:MM:SS timezone]	[Specific Complaint]	[Direct quote or summary from review]	[Severity rating]
      ...	...	...	...	...
      Table 2: Positive Reviews
      Username	Date & Time	Positive Aspect	Citation (Quote or Summary)	Positivity Score (1-10)
      [Username]	[YYYY-MM-DD HH:MM:SS timezone]	[Positive feature or experience]	[Direct quote or summary from review]	[Score 1-10]
      ...	...	...	...	...

      ---

      App Reviews:
      The following is a series of concatenated JSON objects, where each object represents a single app review. Please parse each JSON object individually. For each review, extract the following fields: \`author.name.label\` (Username), \`updated.label\` (Date & Time), \`content.label\` (Review Content), \`im:rating.label\` (Rating), and \`title.label\` (Review Title).

      ${appReviews}

      ---

      Please provide the analysis in the specified format, ensuring that all tasks (summarization, recommendations, metadata extraction, categorization, and table generation) are performed based on the individual parsed reviews.
    `;

    const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;

    if (!OPENROUTER_API_KEY) {
      return NextResponse.json({ message: 'OpenRouter API key is not configured.' }, { status: 500 });
    }

    const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'https://leanalytics-playbook.vercel.app',
        'X-Title': 'Playbook App',
      },
      body: JSON.stringify({
        model: 'anthropic/claude-3.5-haiku',
        messages: [
          ...(messages || []), // Prepend any incoming messages (e.g., the forget prompt)
          {
            role: 'user',
            content: prompt,
          },
        ],
      }),
    });

    if (!openRouterResponse.ok) {
      const errorText = await openRouterResponse.text();
      console.error('OpenRouter API error:', errorText);
      return NextResponse.json({ message: `OpenRouter API error: ${JSON.stringify(errorText)}` }, { status: openRouterResponse.status });
    }

    const openRouterData = await openRouterResponse.json();
    const llmResult = openRouterData.choices[0]?.message?.content || 'No analysis generated.';

    return NextResponse.json({ result: llmResult });

  } catch (error: any) {
    console.error('API route error:', error);
    return NextResponse.json({ message: error.message || 'An unexpected error occurred.' }, { status: 500 });
  }
}
