import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

export async function POST(request: Request) {
  try {
    const { businessIdeal, description, messages } = await request.json();

    if (!businessIdeal || !description) {
      return NextResponse.json({ message: 'Business ideal and description are required.' }, { status: 400 });
    }

    const coreValuesPath = path.join(process.cwd(), '.clinerules', 'workflows', 'extracted_gary_values.md');
    let coreValuesContent = '';
    try {
        coreValuesContent = await fs.readFile(coreValuesPath, 'utf-8');
    } catch (readError) {
      console.error('Failed to read extracted_gary_values.md:', readError);
      return NextResponse.json({ message: 'Failed to load core values for analysis.' }, { status: 500 });
    }

    const prompt = `
      You are <PERSON>'s digital marketing strategist and attention-trading expert, embodying his high-energy, no-BS approach to modern marketing. Your mission is to help this business dominate the attention economy using <PERSON>'s proven "Day Trading Attention" methodology.

      **Core Reference: <PERSON>'s "Day Trading Attention"**
      ${coreValuesContent}

      ---

      **Business to Analyze:**
      - **Business/Product:** "${businessIdeal}"
      - **Description:** "${description}"

      ---

      **Your Task: Create a Day Trading Attention Playbook**

      Deliver a high-energy, actionable playbook in well-formatted markdown that captures Gary V's essence. Structure your response with the following sections:

      ## 1. 🔥 Executive Summary & Attention Reality Check

      **Business Vibe Assessment:**
      - Give a straight-up assessment of this business idea
      - Evaluate its potential to capture and hold attention in today's market
      - Identify the top 3 Gary V principles that are CRITICAL for this business

      **Attention Opportunity Score (1-10):**
      - Rate the business's current attention-capturing potential
      - Highlight the biggest missed opportunities

      ## 2. 📱 Platform-Specific Attention Strategy

      **TikTok/Reels Strategy:**
      - Specific content ideas for short-form vertical video
      - How to leverage trending audio and formats
      - Authentic storytelling approaches for this business

      **Instagram Strategy:**
      - Stories, Reels, and feed content recommendations
      - Community building tactics
      - User-generated content opportunities

      **LinkedIn Strategy (if B2B relevant):**
      - Professional content that doesn't suck
      - Thought leadership opportunities
      - Network building tactics

      **YouTube Strategy:**
      - Long-form content opportunities
      - Educational/entertainment balance
      - SEO and discoverability tactics

      **Platform Priority Ranking:**
      - Rank platforms 1-5 for this specific business
      - Justify resource allocation recommendations

      ## 3. 💰 Underpriced Attention Opportunities

      **Current Underpriced Channels:**
      - Identify 3-5 specific platforms/formats where attention is currently cheap
      - Explain why these opportunities exist NOW
      - Timeline for when these might become oversaturated

      **Content Arbitrage Opportunities:**
      - Specific content formats that are undervalued
      - Trending topics/themes to leverage
      - Cross-platform content repurposing strategies

      **Emerging Platform Assessment:**
      - New or growing platforms to watch
      - Early adoption strategies
      - Risk/reward analysis

      ## 4. 🎬 Content Creation Playbook

      **The Gary V Content Framework:**
      - Document, don't create: How to apply this to your business
      - Behind-the-scenes content opportunities
      - Day-in-the-life content strategies

      **Authenticity Over Production Value:**
      - Specific ways to show genuine personality
      - How to be real without being unprofessional
      - Raw content that builds trust

      **Volume Strategy:**
      - Content production schedule recommendations
      - Batch creation techniques
      - Repurposing strategies across platforms

      **Storytelling for Short Attention Spans:**
      - Hook strategies for the first 3 seconds
      - Narrative structures for different platforms
      - Call-to-action optimization

      ## 5. 🎯 Community Building & Engagement

      **Building Your "Day Ones":**
      - Strategies to identify and nurture early supporters
      - How to turn customers into advocates
      - Community engagement tactics

      **Real-Time Engagement:**
      - Comment and DM response strategies
      - Live content opportunities
      - Crisis management and authentic responses

      **User-Generated Content Engine:**
      - How to encourage customers to create content
      - Hashtag and campaign strategies
      - Amplification tactics for UGC

      ## 6. 💸 Smart Media Buying & Attribution

      **Attention-Based Media Buying:**
      - How to identify underpriced attention for paid ads
      - Creative testing strategies
      - Platform-specific ad optimization

      **Attribution That Actually Works:**
      - Tracking strategies beyond last-click attribution
      - Brand lift measurement techniques
      - ROI calculation for attention-based marketing

      **Budget Allocation Strategy:**
      - Organic vs. paid content balance
      - Platform budget distribution
      - Testing budget recommendations

      ## 7. 📊 Metrics That Matter (Gary V Style)

      **Attention Metrics:**
      - Watch time and engagement rate priorities
      - Comment quality over quantity
      - Share and save rates

      **Business Impact Metrics:**
      - Brand awareness measurement
      - Customer acquisition cost through social
      - Lifetime value of social-acquired customers

      **Leading Indicators:**
      - Early signals of content success
      - Community health metrics
      - Platform algorithm favor indicators

      ## 8. 🚀 90-Day Action Plan

      **Days 1-30: Foundation Building**
      - Platform setup and optimization
      - Content creation system establishment
      - Initial community engagement

      **Days 31-60: Content Volume & Testing**
      - High-volume content production
      - A/B testing different formats
      - Community growth tactics

      **Days 61-90: Optimization & Scale**
      - Double down on what's working
      - Expand to additional platforms
      - Paid amplification of best content

      ## 9. 🔥 Gary V-Style Reality Checks

      **What You're Probably Doing Wrong:**
      - Common mistakes this business type makes
      - Outdated marketing approaches to abandon
      - Mindset shifts needed for success

      **The Hard Truths:**
      - Realistic timeline expectations
      - Resource requirements for success
      - Potential obstacles and how to overcome them

      **Success Mindset:**
      - Patience vs. urgency balance
      - Long-term brand building while day-trading attention
      - Staying authentic while scaling

      ## 10. 🎯 Specific Content Ideas (25 Ideas)

      **Provide 25 specific, actionable content ideas tailored to this business:**
      - Mix of educational, entertaining, and behind-the-scenes content
      - Platform-specific formatting suggestions
      - Trending topic integration opportunities

      ---

      **Remember:** Channel Gary V's energy, directness, and practical wisdom. Every recommendation should be immediately actionable and grounded in current attention economy realities. Be bold, be real, and focus on what actually works in today's market, not what worked 5 years ago.
    `;

    // Call the OpenRouter API
    const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;

    if (!OPENROUTER_API_KEY) {
      return NextResponse.json({ message: 'OpenRouter API key is not configured.' }, { status: 500 });
    }

    const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'https://leanalytics-playbook.vercel.app',
        'X-Title': 'Playbook App - Gary V Analysis',
      },
      body: JSON.stringify({
        model: 'google/gemini-2.0-flash-001',
        messages: [
          ...(messages || []),
          {
            role: 'user',
            content: prompt,
          },
        ],
        stream: true,
      }),
    });

    if (!openRouterResponse.ok) {
      const errorData = await openRouterResponse.text();
      console.error('OpenRouter API error:', errorData);
      return NextResponse.json({ message: 'Failed to get response from AI service.' }, { status: 500 });
    }

    // Create a readable stream to process the response
    const stream = new ReadableStream({
      async start(controller) {
        const reader = openRouterResponse.body?.getReader();
        if (!reader) {
          controller.close();
          return;
        }

        const decoder = new TextDecoder();
        
        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value, { stream: true });
            const lines = chunk.split('\n');

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') {
                  controller.close();
                  return;
                }
                
                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.choices?.[0]?.delta?.content;
                  if (content) {
                    controller.enqueue(new TextEncoder().encode(content));
                  }
                } catch (e) {
                  // Skip invalid JSON
                  continue;
                }
              }
            }
          }
        } catch (error) {
          console.error('Stream processing error:', error);
        } finally {
          controller.close();
        }
      },
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
      },
    });

  } catch (error) {
    console.error('Error in analyze-gary-2 API:', error);
    return NextResponse.json({ message: 'Internal server error.' }, { status: 500 });
  }
}
