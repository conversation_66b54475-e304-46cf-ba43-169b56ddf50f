import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

export async function POST(request: Request) {
  try {
    const { businessIdeal, description, messages } = await request.json(); // Destructure messages from request body

    if (!businessIdeal || !description) {
      return NextResponse.json({ message: 'Business ideal and description are required.' }, { status: 400 });
    }

    // Read the extracted core values
    const coreValuesPath = path.join(process.cwd(), '.clinerules', 'workflows', 'extracted_gary_values.md');
    let coreValuesContent = '';
    try {
      coreValuesContent = await fs.readFile(coreValuesPath, 'utf-8');
    } catch (readError) {
      console.error('Failed to read extracted_gary_values.md:', readError);
      return NextResponse.json({ message: 'Failed to load core values for analysis.' }, { status: 500 });
    }

    // Construct the prompt for the LLM
    const prompt = `
      You are a master of digital marketing and brand strategy, embodying the hustle and insight of <PERSON>. Your analysis is direct, practical, and laser-focused on winning the "Day Trading Attention" game.

      A user has a business idea. Your job is to turn it into an attention-grabbing powerhouse using Gary V's core principles.

      **Reference Principles: Gary Vaynerchuk's "Day Trading Attention"**
      ${coreValuesContent}

      ---

      **User's Business Concept**
      - **Ideal:** "${businessIdeal}"
      - **Description:** "${description}"

      ---

      **Your Task: Create an Attention-Trading Playbook**

      Deliver a high-energy, actionable playbook in well-formatted markdown. Follow this structure precisely:

      **1. Executive Summary & Core Vibe**
         - Give a no-nonsense summary of the business ideal.
         - Assess its alignment with Gary V's high-energy, audience-first approach.
         - Identify the top 3 "Day Trading Attention" principles that are non-negotiable for this ideal.

      **2. Attention Capture Score (out of 100)**
         - Provide a brutally honest score on the ideal's potential to capture and hold attention.
         - **Platform Fit (25 pts):** Justify the score.
         - **Content Scalability (25 pts):** Justify the score.
         - **Monetization Potential (25 pts):** Justify the score.
         - **Competitive Edge (25 pts):** Justify the score.
         - **Verdict & Reality Check:** Deliver a final verdict and highlight the biggest risks and untapped opportunities.

      **3. "Crush It" Quick Wins**
         - List 3-5 immediate, high-impact actions the user can take to start trading attention effectively.

      **4. The "Content Machine" Feature Roadmap (20 Features)**
         - Design a roadmap to build a content-generating and attention-analyzing machine.
         - **Content Creation (5):** Tools for rapid, relevant content production.
         - **Platform Optimization (5):** Features to win on specific platforms.
         - **Audience Engagement (5):** Tools to build a real community.
         - **Attention Analytics (5):** Features to measure what matters.
         - *For each feature, specify its name, a brief description, the Gary V principle it embodies, and its attention-grabbing impact.*

      **5. Strategic Execution Plan**
         - **Platform-Specific Strategy:** Outline a concrete content strategy for each key platform (TikTok, Instagram, LinkedIn, etc.).
         - **Content Distribution Model:** Provide a detailed framework for content creation, repurposing (the "1:1:1" model), and distribution.
         - **Monetization Roadmap:** Suggest 3-5 clear monetization models with a phased implementation timeline.

      Your analysis must be packed with actionable advice that empowers the user to build a brand that commands attention.
    `;

    // Call the OpenRouter API
    const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY; // Ensure this is set in your environment variables

    if (!OPENROUTER_API_KEY) {
      return NextResponse.json({ message: 'OpenRouter API key is not configured.' }, { status: 500 });
    }

    const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'https://leanalytics-playbook.vercel.app',
        'X-Title': 'Playbook App',
      },
      body: JSON.stringify({
        model: 'google/gemini-2.0-flash-001',
        messages: [
          ...(messages || []), // Prepend any incoming messages (e.g., the forget prompt)
          {
            role: 'user',
            content: prompt,
          },
        ],
        stream: true, // Enable streaming
      }),
    });

    if (!openRouterResponse.ok) {
      let errorData;
      try {
        errorData = await openRouterResponse.json();
      } catch (jsonError) {
        // If it's not JSON, get it as text
        errorData = await openRouterResponse.text();
      }

      const errorMessage = typeof errorData === 'object' && errorData !== null && 'message' in errorData
        ? errorData.message
        : String(errorData); // Ensure it's a string

      console.error('OpenRouter API error:', errorMessage);
      return NextResponse.json({ message: `OpenRouter API error: ${errorMessage}` }, { status: openRouterResponse.status });
    }

    // Create a TransformStream to process the streamed response
    const stream = new ReadableStream({
      async start(controller) {
        const reader = openRouterResponse.body?.getReader();
        if (!reader) {
          controller.close();
          return;
        }

        const decoder = new TextDecoder();
        let buffer = '';

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            buffer += decoder.decode(value, { stream: true });

            // Process each line (chunk)
            const lines = buffer.split('\n');
            buffer = lines.pop() || ''; // Keep the last incomplete line in buffer

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const jsonStr = line.substring(6);
                if (jsonStr === '[DONE]') {
                  controller.close();
                  return;
                }
                try {
                  const data = JSON.parse(jsonStr);
                  // OpenRouter's streaming format for Gemini might have content in 'text' or 'content'
                  const content = data.choices[0]?.delta?.text || data.choices[0]?.delta?.content || '';
                  if (content) {
                    controller.enqueue(new TextEncoder().encode(content));
                  }
                } catch (parseError) {
                  console.error('Error parsing stream chunk:', parseError);
                  // If parsing fails, enqueue the raw line to avoid data loss, or handle as appropriate
                  controller.enqueue(new TextEncoder().encode(`[PARSE_ERROR] ${line}\n`));
                }
              } else if (line.trim() !== '') {
                // Handle non-data lines, e.g., comments or other messages from OpenRouter
                console.warn('Received non-data line in stream:', line);
                // Optionally enqueue these lines if they contain relevant info, or just log
              }
            }
          }
        } catch (readError) {
          console.error('Error reading stream:', readError);
          controller.error(readError);
        } finally {
          controller.close();
        }
      },
    });

    return new NextResponse(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8', // Use text/plain for streaming raw text
        'Transfer-Encoding': 'chunked',
      },
    });

  } catch (error: any) {
    console.error('API route error:', error);
    return NextResponse.json({ message: error.message || 'An unexpected error occurred.' }, { status: 500 });
  }
}
