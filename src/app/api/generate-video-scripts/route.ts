import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;

    if (!OPENROUTER_API_KEY) {
      return NextResponse.json({ message: 'OpenRouter API key is not configured.' }, { status: 500 });
    }

    const { videoAnalysis, productName, productDescription, targetAudience, callToAction, scriptDirection, includeViralAnatomy } = await request.json();

    if (!productName || !productDescription || !targetAudience || !callToAction || !scriptDirection) {
      return NextResponse.json({ message: 'Missing required fields.' }, { status: 400 });
    }

    // Build behavioral science context from video analysis if available and requested
    let behavioralScienceContext = '';
    if (includeViralAnatomy && videoAnalysis && videoAnalysis.viralAnatomy && videoAnalysis.viralAnatomy.length > 0) {
      behavioralScienceContext = `
VIRAL ANATOMY INSIGHTS FROM UPLOADED VIDEO ANALYSIS:
${videoAnalysis.viralAnatomy.map((element: any, index: number) => `
${index + 1}. ${element.name}
   - Explanation: ${element.explanation}
   - Video Example: ${element.videoExample}
`).join('')}

IMPORTANT: Incorporate these viral anatomy elements into the scripts to replicate the viral success pattern of the analyzed video.
`;
    } else {
      behavioralScienceContext = `
BEHAVIORAL SCIENCE PRINCIPLES TO APPLY:
1. Hook Principle - Start with attention-grabbing opening (curiosity, surprise, controversy)
2. Emotional Connection - Trigger emotions like joy, surprise, fear, or aspiration
3. Relatability & Identity Expression - Make viewers feel seen and understood
4. Concise and Engaging Storytelling - Tell stories quickly and effectively
5. Shareability - Create content people want to share with others
6. Timeliness - Connect to current trends, events, or cultural moments
7. Authenticity - Make content feel genuine and trustworthy
`;
    }

    const prompt = `You are an elite video script writer specializing in viral content creation for CapCut AI video generation. Create 20 FULL SCRIPTS (complete narrative text) that can be directly input into CapCut's AI video creation feature.

${behavioralScienceContext}

PRODUCT INFORMATION:
- Product/Business: ${productName}
- Description: ${productDescription}
- Target Audience: ${targetAudience}
- Call to Action: ${callToAction}

SCRIPT DIRECTION:
${scriptDirection}

CRITICAL REQUIREMENTS FOR FULL SCRIPTS:
1. Generate exactly 20 COMPLETE, FULL-LENGTH scripts
2. Each script should be FULL NARRATIVE TEXT suitable for CapCut AI
3. NO scene descriptions, dialogue, or action directions - PURE SPOKEN CONTENT
4. Calculate proper word count based on duration:
   - 10 seconds = 30-40 words (energetic, fast-paced)
   - 15 seconds = 45-60 words (quick, punchy)
   - 30 seconds = 75-90 words (standard pace)
   - 45 seconds = 110-135 words (moderate pace)
   - 60 seconds = 150-180 words (conversational)
   - 90 seconds = 225-270 words (detailed)
   - 2 minutes = 300-360 words (comprehensive)

5. Apply behavioral science principles to maximize engagement
6. Follow the exact style, tone, and direction specified in: "${scriptDirection}"
7. Include strong hooks, clear value propositions, and compelling CTAs
8. Vary the scripts to provide different angles and approaches
9. Each script should be COMPLETE and READY TO USE in CapCut
10. Match the energy level and tone specified in the direction

SCRIPT WRITING GUIDELINES:
- Write FULL, COMPLETE scripts that tell the entire story
- Use the exact tone and style specified in the script direction
- Include natural transitions and flow
- End with clear, compelling call-to-actions
- Make each script unique with different hooks and approaches
- Ensure scripts are the RIGHT LENGTH for the specified duration
- Write as if speaking directly to the target audience
- Include emotional triggers and persuasive elements

OUTPUT FORMAT:
Respond with valid JSON only:
{
  "scripts": [
    {
      "id": 1,
      "script": "Your complete full-length script text here that matches the exact word count for the specified duration...",
      "wordCount": 85,
      "estimatedDuration": "30 seconds"
    },
    {
      "id": 2,
      "script": "Your second complete full-length script text here...",
      "wordCount": 87,
      "estimatedDuration": "30 seconds"
    }
    // ... continue for all 20 scripts
  ]
}

Each script must be a COMPLETE, FULL-LENGTH narrative that drives ${callToAction} and follows the direction: "${scriptDirection}".`;

    console.log('Sending request to OpenRouter for video scripts generation...');

    // Make request to OpenRouter
    const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'https://leanalytics-playbook.vercel.app',
        'X-Title': 'Playbook App - Video Scripts',
      },
      body: JSON.stringify({
        model: 'moonshotai/kimi-k2',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        response_format: { type: "json_object" },
        stream: true,
        temperature: 0.8 // Add creativity while maintaining structure
      }),
    });

    if (!openRouterResponse.ok) {
      const errorText = await openRouterResponse.text();
      console.error('OpenRouter API error:', errorText);
      return NextResponse.json({ message: `OpenRouter API error: ${errorText}` }, { status: openRouterResponse.status });
    }

    // Create a readable stream to handle the response
    const stream = new ReadableStream({
      async start(controller) {
        const reader = openRouterResponse.body?.getReader();
        if (!reader) {
          controller.error(new Error('No response body'));
          return;
        }

        try {
          let buffer = '';

          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = new TextDecoder().decode(value);
            const lines = chunk.split('\n');

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') continue;

                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.choices?.[0]?.delta?.content;
                  if (content) {
                    buffer += content;
                    // Send only the new content chunk
                    controller.enqueue(new TextEncoder().encode(content));
                  }
                } catch (parseError) {
                  // Skip invalid JSON chunks
                  continue;
                }
              }
            }
          }
        } catch (error) {
          console.error('Stream processing error:', error);
          const errorMessage = `Stream error: ${error instanceof Error ? error.message : 'Unknown error'}`;
          controller.enqueue(new TextEncoder().encode(errorMessage));
          controller.error(error);
        } finally {
          console.log('Stream processing finished');
          controller.close();
        }
      },
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
      },
    });

  } catch (error) {
    console.error('Error in generate-video-scripts API:', error);
    return NextResponse.json({ message: 'Internal server error.' }, { status: 500 });
  }
}
