import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

export async function POST(request: Request) {
  try {
    const { businessIdeal, description, messages } = await request.json();

    if (!businessIdeal || !description) {
      return NextResponse.json({ message: 'Business ideal and description are required.' }, { status: 400 });
    }

    const bookContentPath = path.join(process.cwd(), 'public', 'book_summary.md');
    let bookContent = '';
    try {
        bookContent = await fs.readFile(bookContentPath, 'utf-8');
    } catch (readError) {
      console.error('Failed to read book_summary.md:', readError);
      return NextResponse.json({ message: 'Failed to load book content for analysis.' }, { status: 500 });
    }

    const prompt = `
      You are a world-class Jobs to Be Done strategist, deeply versed in <PERSON>'s revolutionary framework. Your mission is to help this business understand the true "job" their customers are hiring them to do and provide actionable insights for success.

      **Core Reference: <PERSON>'s "Competing Against Luck"**
      ${bookContent}

      ---

      **Business to Analyze:**
      - **Business/Product:** "${businessIdeal}"
      - **Description:** "${description}"

      ---

      **Your Task: Deep Jobs to Be Done Analysis**

      Provide a comprehensive, actionable analysis using the JTBD framework. Structure your response in well-formatted markdown with the following sections:

      ## 1. 🎯 The Core Job Identification

      **Primary Job to Be Done:**
      - Clearly articulate the specific "job" customers are hiring this business to accomplish
      - Describe the functional, emotional, and social dimensions of this job
      - Explain the circumstances and context when this job arises

      **Job Statement Format:**
      "When [situation/circumstance], I want to [desired outcome], so I can [ultimate benefit/progress]."

      ## 2. 🔄 The Four Forces Analysis

      Analyze each of the four forces that influence customer decisions:

      **🚀 PUSH (Current Struggles):**
      - What specific frustrations, pain points, or inadequacies are customers experiencing with current solutions?
      - What is "pushing" them away from their current situation?

      **🎯 PULL (Desired Future):**
      - What aspirational outcome or progress do customers envision?
      - What is "pulling" them toward a better solution?

      **😰 ANXIETY (Fears & Concerns):**
      - What worries or doubts might customers have about adopting this new solution?
      - What could prevent them from "hiring" this product despite the push and pull?

      **🔄 HABIT (Inertia & Existing Behaviors):**
      - What existing habits, routines, or incumbent solutions create resistance to change?
      - What makes the status quo "sticky" despite its problems?

      ## 3. 🌍 Circumstances & Context Mapping

      **When & Where the Job Arises:**
      - Specific situations, times, or contexts when customers experience this job
      - Environmental factors that trigger the need for this solution

      **Who's Involved:**
      - Primary decision-makers and influencers in the "hiring" process
      - How social dynamics affect the job

      ## 4. 🚫 Non-Consumption Analysis

      **Underserved Markets:**
      - Identify groups who currently use NO solution for this job (non-consumers)
      - Why aren't they using existing alternatives?
      - What barriers prevent them from getting this job done?

      **Market Expansion Opportunities:**
      - How could you make the solution accessible to non-consumers?
      - What would need to change to serve these underserved segments?

      ## 5. 🥊 True Competition Redefinition

      **Real Competitors (Beyond Obvious Ones):**
      - What are customers actually "hiring" instead of your solution?
      - Include non-obvious alternatives, workarounds, and "doing nothing"
      - Map the competitive landscape from a jobs perspective

      ## 6. 🎯 Strategic Recommendations

      **Job Performance Optimization:**
      - Specific ways to better perform the core job
      - How to address each of the four forces more effectively

      **Product/Service Enhancements:**
      - Features or capabilities that would better serve the job
      - What to eliminate that doesn't contribute to job performance

      **Go-to-Market Strategy:**
      - How to communicate the job you solve (not just features)
      - Messaging that resonates with the job's emotional and social dimensions

      ## 7. 🛠️ Implementation Roadmap

      **Immediate Actions (Next 30 Days):**
      - 3-5 specific steps to better understand and serve the job

      **Short-term Initiatives (3-6 Months):**
      - Key improvements to enhance job performance

      **Long-term Vision (6-12 Months):**
      - Strategic moves to own this job in the market

      ## 8. 🔍 Research & Validation Questions

      **Customer Interview Questions:**
      - Specific questions to validate your job hypothesis
      - Focus on circumstances, struggles, and desired progress

      **Observational Research:**
      - What customer behaviors to observe
      - How to uncover unspoken needs and workarounds

      ---

      **Remember:** Focus on causality (WHY customers choose solutions) rather than correlation. Every recommendation should be grounded in helping customers make progress on their specific job. Be specific, actionable, and deeply insightful.
    `;

    // Call the OpenRouter API
    const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;

    if (!OPENROUTER_API_KEY) {
      return NextResponse.json({ message: 'OpenRouter API key is not configured.' }, { status: 500 });
    }

    const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'https://leanalytics-playbook.vercel.app',
        'X-Title': 'Playbook App - JTBD Analysis',
      },
      body: JSON.stringify({
        model: 'moonshotai/kimi-k2',
        messages: [
          ...(messages || []),
          {
            role: 'user',
            content: prompt,
          },
        ],
        stream: true,
      }),
    });

    if (!openRouterResponse.ok) {
      const errorData = await openRouterResponse.text();
      console.error('OpenRouter API error:', errorData);
      return NextResponse.json({ message: 'Failed to get response from AI service.' }, { status: 500 });
    }

    // Create a readable stream to process the response
    const stream = new ReadableStream({
      async start(controller) {
        const reader = openRouterResponse.body?.getReader();
        if (!reader) {
          controller.close();
          return;
        }

        const decoder = new TextDecoder();

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value, { stream: true });
            const lines = chunk.split('\n');

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') {
                  controller.close();
                  return;
                }

                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.choices?.[0]?.delta?.content;
                  if (content) {
                    controller.enqueue(new TextEncoder().encode(content));
                  }
                } catch (e) {
                  // Skip invalid JSON
                  continue;
                }
              }
            }
          }
        } catch (error) {
          console.error('Stream processing error:', error);
        } finally {
          controller.close();
        }
      },
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
      },
    });

  } catch (error) {
    console.error('Error in analyze-clayton-2 API:', error);
    return NextResponse.json({ message: 'Internal server error.' }, { status: 500 });
  }
}
