import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const GEMINI_API_KEY = process.env.GEMINI_API_KEY;

    if (!GEMINI_API_KEY) {
      return NextResponse.json({ message: 'Gemini API key is not configured.' }, { status: 500 });
    }

    let formData;
    try {
      formData = await request.formData();
    } catch (error) {
      console.error('Error parsing form data:', error);
      if (error instanceof Error && error.message.includes('PayloadTooLargeError')) {
        return NextResponse.json({ message: 'Video file is too large. Maximum size is 4MB.' }, { status: 413 });
      }
      return NextResponse.json({ message: 'Failed to process uploaded file.' }, { status: 400 });
    }

    const videoFile = formData.get('video') as File;

    if (!videoFile) {
      return NextResponse.json({ message: 'Video file is required.' }, { status: 400 });
    }

    // Handle uploaded video file
    // Vercel serverless functions have a 4.5MB payload limit, so we need to be more conservative
    const maxSize = 4 * 1024 * 1024; // 4MB limit for serverless functions
    if (videoFile.size > maxSize) {
      return NextResponse.json({ message: 'Video file is too large. Maximum size is 4MB for serverless processing.' }, { status: 400 });
    }

    let videoAnalysis: any = null;

      try {
        console.log('Processing video file:', videoFile.name, 'Size:', videoFile.size, 'Type:', videoFile.type);
        const arrayBuffer = await videoFile.arrayBuffer();
        const base64 = Buffer.from(arrayBuffer).toString('base64');
        console.log('Video file converted to base64, length:', base64.length);

        const geminiResponse = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-001:generateContent', {
          method: 'POST',
          headers: {
            'x-goog-api-key': GEMINI_API_KEY,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [
              {
                parts: [
                  {
                    inline_data: {
                      mime_type: videoFile.type,
                      data: base64
                    }
                  },
                  {
                    text: `Analyze this viral video based on the specific behavioral science anatomy that makes content go viral.

CRITICAL: Respond ONLY with valid JSON. No other text.

Analyze the video for these 7 key viral anatomy elements:

1. HOOK - How does it grab attention in the first 3 seconds?
2. EMOTIONAL CONNECTION - What emotions does it trigger (joy, surprise, fear, anger, etc.)?
3. RELATABILITY & IDENTITY EXPRESSION - How does it make viewers feel seen/understood?
4. CONCISE AND ENGAGING STORYTELLING - How does it tell a story quickly and effectively?
5. SHAREABILITY - What makes people want to share this content?
6. TIMELINESS - How does it tap into current trends, events, or cultural moments?
7. AUTHENTICITY - What makes it feel genuine and trustworthy?

For each element found, provide: name, explanation, and specific video example.

JSON format (copy exactly):
{
  "viralAnatomy": [
    {
      "name": "Hook",
      "explanation": "How the video grabs attention in the opening seconds",
      "videoExample": "Specific example from this video"
    },
    {
      "name": "Emotional Connection",
      "explanation": "What emotions are triggered and how",
      "videoExample": "Specific example from this video"
    },
    {
      "name": "Relatability & Identity Expression",
      "explanation": "How viewers can relate or express identity",
      "videoExample": "Specific example from this video"
    },
    {
      "name": "Concise and Engaging Storytelling",
      "explanation": "How the story is told effectively",
      "videoExample": "Specific example from this video"
    },
    {
      "name": "Shareability",
      "explanation": "What makes it shareable",
      "videoExample": "Specific example from this video"
    },
    {
      "name": "Timeliness",
      "explanation": "How it connects to current trends/moments",
      "videoExample": "Specific example from this video"
    },
    {
      "name": "Authenticity",
      "explanation": "What makes it feel genuine",
      "videoExample": "Specific example from this video"
    }
  ]
}`
                  }
                ]
              }
            ]
          }),
        });

        if (!geminiResponse.ok) {
          const errorData = await geminiResponse.text();
          console.error('Gemini API error:', errorData);
          return NextResponse.json({ message: 'Failed to analyze video with Gemini API.' }, { status: 500 });
        }

        let geminiData;
        try {
          geminiData = await geminiResponse.json();
        } catch (parseError) {
          console.error('Failed to parse Gemini response as JSON:', parseError);
          return NextResponse.json({ message: 'Invalid response from Gemini API.' }, { status: 500 });
        }
        const analysisText = geminiData.candidates?.[0]?.content?.parts?.[0]?.text;

        if (!analysisText) {
          return NextResponse.json({ message: 'No analysis received from Gemini API.' }, { status: 500 });
        }

        console.log('Raw Gemini response:', analysisText);

        // Parse the JSON response from Gemini
        try {
          let jsonString = '';

          // First, try to extract JSON from markdown code blocks
          const codeBlockMatch = analysisText.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
          if (codeBlockMatch) {
            jsonString = codeBlockMatch[1];
            console.log('Extracted JSON from code block:', jsonString);
          } else {
            // Fallback: Try to find JSON by looking for the first { and last }
            const firstBrace = analysisText.indexOf('{');
            const lastBrace = analysisText.lastIndexOf('}');

            if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
              jsonString = analysisText.substring(firstBrace, lastBrace + 1);
              console.log('Extracted JSON string:', jsonString);
            } else {
              throw new Error('No JSON found in response');
            }
          }

          // Parse the JSON
          videoAnalysis = JSON.parse(jsonString);

          // Validate and fix the structure
          if (!videoAnalysis.viralAnatomy) {
            console.warn('Missing viralAnatomy array in videoAnalysis, initializing empty array');
            videoAnalysis.viralAnatomy = [];
          } else if (!Array.isArray(videoAnalysis.viralAnatomy)) {
            console.warn('viralAnatomy is not an array, converting to array');
            videoAnalysis.viralAnatomy = [videoAnalysis.viralAnatomy];
          }

          // Add fileName if missing
          if (!videoAnalysis.fileName) {
            videoAnalysis.fileName = videoFile.name;
          }

          console.log('Successfully parsed video analysis with', videoAnalysis.viralAnatomy.length, 'viral anatomy elements');

        } catch (parseError) {
          console.error('Failed to parse Gemini response:', parseError);
          console.error('Raw response text:', analysisText);
          // Create a fallback response
          videoAnalysis = {
            fileName: videoFile.name,
            viralAnatomy: []
          };
        }

      } catch (error) {
        console.error('Error processing video file:', error);
        return NextResponse.json({ message: 'Failed to process video file.' }, { status: 500 });
      }

    // Validate the videoAnalysis object before returning
    if (!videoAnalysis || typeof videoAnalysis !== 'object') {
      console.error('Invalid videoAnalysis object:', videoAnalysis);
      return NextResponse.json({
        message: 'Failed to generate valid video analysis.'
      }, { status: 500 });
    }

    // Ensure the viralAnatomy array exists
    if (!videoAnalysis.viralAnatomy || !Array.isArray(videoAnalysis.viralAnatomy)) {
      console.warn('Missing viralAnatomy array in videoAnalysis, initializing empty array');
      videoAnalysis.viralAnatomy = [];
    }

    return NextResponse.json({ videoAnalysis });

  } catch (error) {
    console.error('Error in analyze-video-science API:', error);
    return NextResponse.json({ message: 'Internal server error.' }, { status: 500 });
  }
}
