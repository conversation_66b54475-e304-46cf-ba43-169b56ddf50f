import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

export async function POST(request: Request) {
  try {
    const { businessIdeal, description, messages } = await request.json(); // Destructure messages from request body

    if (!businessIdeal || !description) {
      return NextResponse.json({ message: 'Business ideal and description are required.' }, { status: 400 });
    }

    // Read the extracted core values
    const coreValuesPath = path.join(process.cwd(), '.clinerules', 'workflows', 'extracted_core_values.md');
    let coreValuesContent = '';
    try {
      coreValuesContent = await fs.readFile(coreValuesPath, 'utf-8');
    } catch (readError) {
      console.error('Failed to read extracted_core_values.md:', readError);
      return NextResponse.json({ message: 'Failed to load core values for analysis.' }, { status: 500 });
    }

    // Construct the prompt for the LLM
    const prompt = `
      You are a world-class business strategist, channeling the entrepreneurial spirit of Lei Jun. Your analysis is sharp, actionable, and rooted in the principles from "Xiaomi Entrepreneurial Thinking."

      A user has presented a business concept. Your mission is to forge it into a market-leading product by applying Lei Jun's methodology.

      **Reference Principles: Lei Jun's Core Values**
      ${coreValuesContent}

      ---

      **User's Business Concept**
      - **Ideal:** "${businessIdeal}"
      - **Description:** "${description}"

      ---

      **Your Task: Create a Strategic Blueprint**

      Produce a detailed, yet precise, strategic blueprint in well-formatted markdown. Follow this structure exactly:

      **1. Executive Summary & Core Alignment**
         - Briefly summarize the business ideal.
         - Assess its fundamental strengths and weaknesses against Lei Jun's philosophy.
         - Identify the top 3 principles that will be most critical for this ideal's success.

      **2. Go-to-Market (GTM) Viability Score (out of 100)**
         - Provide a brutally honest GTM score.
         - **Market Fit (25 pts):** Justify the score.
         - **Differentiation (25 pts):** Justify the score.
         - **Execution Feasibility (25 pts):** Justify the score.
         - **Growth Potential (25 pts):** Justify the score.
         - **Verdict & Risks:** Deliver a final verdict, outlining the biggest market risks and opportunities.

      **3. Actionable "Quick Wins"**
         - List 3-5 immediate, high-impact actions the user can take to align with Lei Jun's methodology.

      **4. The "Ultimate Product" Feature Roadmap (50 Features)**
         - Craft a roadmap designed to create an "explosive product" (爆品).
         - **Core (15):** Essential for the value proposition.
         - **Differentiation (10):** Unique selling points.
         - **User Experience (10):** Features for delight and retention.
         - **Growth (5):** To drive acquisition.
         - **Ecosystem (5):** To build a network effect.
         - **Future Innovation (5):** For long-term dominance.
         - *For each feature, specify its name, a brief description, the Lei Jun principle it embodies, and its user impact.*

      **5. Strategic Recommendations**
         - **Principle-by-Principle Analysis:** For each relevant Lei Jun principle, provide a concise analysis of the ideal's current state and concrete recommendations for improvement.
         - **Pricing Strategy:** Propose a simple, high-value pricing model, true to the "性价比" (cost-effectiveness) principle.
         - **Ecosystem Development (5 Components):** Suggest 5 complementary ecosystem components. For each, describe its purpose, how it enhances the core ideal, and its implementation priority.

      Your analysis must be insightful, practical, and transform the user's concept into a potential success story.
    `;

    // Call the OpenRouter API
    const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY; // Ensure this is set in your environment variables

    if (!OPENROUTER_API_KEY) {
      return NextResponse.json({ message: 'OpenRouter API key is not configured.' }, { status: 500 });
    }

    const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'https://leanalytics-playbook.vercel.app',
        'X-Title': 'Playbook App',
      },
      body: JSON.stringify({
        model: 'anthropic/claude-3.5-haiku',
        messages: [
          ...(messages || []), // Prepend any incoming messages (e.g., the forget prompt)
          {
            role: 'user',
            content: prompt,
          },
        ],
        stream: true, // Enable streaming
      }),
    });

    if (!openRouterResponse.ok) {
      let errorData;
      try {
        errorData = await openRouterResponse.json();
      } catch (jsonError) {
        // If it's not JSON, get it as text
        errorData = await openRouterResponse.text();
      }

      const errorMessage = typeof errorData === 'object' && errorData !== null && 'message' in errorData
        ? errorData.message
        : String(errorData); // Ensure it's a string

      console.error('OpenRouter API error:', errorMessage);
      return NextResponse.json({ message: `OpenRouter API error: ${errorMessage}` }, { status: openRouterResponse.status });
    }

    // Create a TransformStream to process the streamed response
    const stream = new ReadableStream({
      async start(controller) {
        const reader = openRouterResponse.body?.getReader();
        if (!reader) {
          controller.close();
          return;
        }

        const decoder = new TextDecoder();
        let buffer = '';

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            buffer += decoder.decode(value, { stream: true });

            // Process each line (chunk)
            const lines = buffer.split('\n');
            buffer = lines.pop() || ''; // Keep the last incomplete line in buffer

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const jsonStr = line.substring(6);
                if (jsonStr === '[DONE]') {
                  controller.close();
                  return;
                }
                try {
                  const data = JSON.parse(jsonStr);
                  // OpenRouter's streaming format for Gemini might have content in 'text' or 'content'
                  const content = data.choices[0]?.delta?.text || data.choices[0]?.delta?.content || '';
                  if (content) {
                    controller.enqueue(new TextEncoder().encode(content));
                  }
                } catch (parseError) {
                  console.error('Error parsing stream chunk:', parseError);
                  // If parsing fails, enqueue the raw line to avoid data loss, or handle as appropriate
                  controller.enqueue(new TextEncoder().encode(`[PARSE_ERROR] ${line}\n`));
                }
              } else if (line.trim() !== '') {
                // Handle non-data lines, e.g., comments or other messages from OpenRouter
                console.warn('Received non-data line in stream:', line);
                // Optionally enqueue these lines if they contain relevant info, or just log
              }
            }
          }
        } catch (readError) {
          console.error('Error reading stream:', readError);
          controller.error(readError);
        } finally {
          controller.close();
        }
      },
    });

    return new NextResponse(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8', // Use text/plain for streaming raw text
        'Transfer-Encoding': 'chunked',
      },
    });

  } catch (error: any) {
    console.error('API route error:', error);
    return NextResponse.json({ message: error.message || 'An unexpected error occurred.' }, { status: 500 });
  }
}
