# Application Workflow

This document outlines the workflow for building an application based on the provided requirements.

## 1. Lei Jun Ebook Methodology - Core Values Extraction

**Objective:** Summarize the given content (Lei Jun ebook methodology) and extract its core values to serve as the "brain/insight" for the application. This will form the foundational principles for improving business ideals.

**Reference:** For the detailed methodology, please refer to [Xiaomi Playbook Methodology](.clinerules/workflows/xiaomiplaybook.md).

**Action:**
- Analyze the Lei Jun ebook content (to be provided separately) to identify key principles, strategies, and philosophies.
- Distill these into a concise set of core values or methodologies.

## 2. Business Ideal Analysis and Improvement

**Objective:** Develop an application feature that takes a user's business ideal (e.g., "Math Solver app") and, using the extracted Lei Jun methodology, suggests improvements and refinements.

**Functionality:**
- Users will input their business ideal and a description.
- The application will use the OpenRouter API to interact with a specified LLM (`anthropic/claude-3.5-haiku`).
- The LLM will process the user's ideal, applying the Lei Jun methodology (from step 1) to provide enhanced and improved business concepts.

**OpenRouter API Details:**
- **API Key:** `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`
- **LLM Example Request:**
  ```bash
  curl https://openrouter.ai/api/v1/chat/completions \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $OPENROUTER_API_KEY" \
    -d '{
    "model": "anthropic/claude-3.5-haiku",
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": "What is in this image?"
          },
          {
            "type": "image_url",
            "image_url": {
              "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg"
            }
          }
        ]
      }
    ]
  }'
  ```

## 3. User Interface (UI) Design

**Objective:** Integrate the new functionality into the existing project dashboard UI.

**Design Specifications:**
- The UI will be based on the current project dashboard.
- **Left Panel:** A new link titled "Lei Jun eBook" will be added to the navigation.
- **Right Panel:**
    - An input box for users to enter their "ideal" (e.g., business idea).
    - A description box for users to provide more details about their ideal.
    - An "Analyse" button to trigger the analysis process.

## 4. Output Display and AI Chatbox

**Objective:** Present the analysis results to the user and provide an interactive AI chat for further queries.

**Functionality:**
- **Output Display:** Once the "Analyse" button is clicked, the improved business ideal generated by the LLM will be displayed below the input box.
- **AI Chatbox:** An integrated AI chatbox will be available for users to ask follow-up questions about the output values or the analysis.

## 5. Data Persistence

**Objective:** Ensure that all generated results and user interactions are persistently stored.

**Requirement:**
- The results of the analysis and potentially the chat history must be saved to ensure data is not lost upon application closure or refresh. The specific storage mechanism (e.g., database, local storage) needs to be determined during implementation.
